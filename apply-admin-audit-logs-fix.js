const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAdminAuditLogsColumn() {
  console.log('🔧 Fixing admin_audit_logs column name issue...\n');

  try {
    // Read the SQL fix file
    const sqlContent = fs.readFileSync('./fix-admin-audit-logs-column.sql', 'utf8');
    
    console.log('📋 Applying database function fix...');
    
    // Execute the SQL fix
    const { error } = await supabase.rpc('exec', {
      sql: sqlContent
    });

    if (error) {
      console.error('❌ Error applying fix:', error);
      process.exit(1);
    }

    console.log('✅ Database function updated successfully');
    
    // Test the function exists
    console.log('\n🧪 Testing function exists...');
    const { data: functions, error: funcError } = await supabase
      .rpc('exec', {
        sql: `
          SELECT routine_name, routine_type 
          FROM information_schema.routines 
          WHERE routine_name = 'process_commission_conversion'
          AND routine_schema = 'public';
        `
      });

    if (funcError) {
      console.error('❌ Error checking function:', funcError);
    } else {
      console.log('✅ Function verification completed');
      console.log('📊 Functions found:', functions);
    }

    // Verify admin_audit_logs table structure
    console.log('\n🔍 Verifying admin_audit_logs table structure...');
    const { data: columns, error: colError } = await supabase
      .rpc('exec', {
        sql: `
          SELECT column_name, data_type, is_nullable
          FROM information_schema.columns 
          WHERE table_name = 'admin_audit_logs' 
          AND table_schema = 'public'
          ORDER BY ordinal_position;
        `
      });

    if (colError) {
      console.error('❌ Error checking table structure:', colError);
    } else {
      console.log('✅ Table structure verified:');
      console.table(columns);
    }

    console.log('\n🎉 Fix completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- ✅ Updated process_commission_conversion function');
    console.log('- ✅ Fixed column names: admin_id → admin_telegram_id');
    console.log('- ✅ Fixed column names: action_type → action');
    console.log('- ✅ Fixed column names: created_at → timestamp');
    console.log('- ✅ Added proper error handling');
    
    console.log('\n🚀 The commission conversion error should now be resolved!');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  }
}

// Run the fix
fixAdminAuditLogsColumn();
