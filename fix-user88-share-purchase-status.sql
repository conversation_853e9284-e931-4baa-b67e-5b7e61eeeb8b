-- Fix User 88's commission conversion share purchase status
-- This record should be 'active' since the commission conversion was approved
-- and the escrow was cleared

-- First, let's see the current state
SELECT 
    id,
    user_id,
    package_name,
    shares_purchased,
    status,
    payment_method,
    created_at
FROM aureus_share_purchases 
WHERE user_id = 88 
    AND package_name = 'Commission Conversion - Phase 0'
    AND id = '21db1e97-a94d-4588-930d-eedc4c38677f';

-- Update the status from 'pending' to 'active' since the commission conversion was approved
UPDATE aureus_share_purchases 
SET 
    status = 'active',
    updated_at = NOW()
WHERE id = '21db1e97-a94d-4588-930d-eedc4c38677f'
    AND user_id = 88
    AND status = 'pending'
    AND package_name = 'Commission Conversion - Phase 0';

-- Verify the update
SELECT 
    id,
    user_id,
    package_name,
    shares_purchased,
    status,
    payment_method,
    created_at,
    updated_at
FROM aureus_share_purchases 
WHERE user_id = 88 
    AND package_name = 'Commission Conversion - Phase 0'
    AND id = '21db1e97-a94d-4588-930d-eedc4c38677f';

-- Show User 88's complete portfolio summary after fix
SELECT 
    'Active Purchases' as type,
    COUNT(*) as count,
    SUM(shares_purchased) as total_shares
FROM aureus_share_purchases 
WHERE user_id = 88 AND status = 'active'
UNION ALL
SELECT 
    'Pending Purchases' as type,
    COUNT(*) as count,
    SUM(shares_purchased) as total_shares
FROM aureus_share_purchases 
WHERE user_id = 88 AND status = 'pending';
