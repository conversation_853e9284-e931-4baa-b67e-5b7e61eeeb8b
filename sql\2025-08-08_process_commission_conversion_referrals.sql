-- Update process_commission_conversion to generate referral commissions with phase-based logic
-- NOTE: Review and apply in Supabase manually

BEGIN;

-- Drop and recreate function (adjust schema names if needed)
CREATE OR REPLACE FUNCTION process_commission_conversion(
  p_conversion_id uuid,
  p_admin_id bigint,
  p_admin_username text
) RETURNS void AS $$
DECLARE
  v_conversion RECORD;
  v_user_id uuid;
  v_phase RECORD;
  v_referral RECORD;
  v_required_usdt numeric;
  v_shares numeric;
  v_is_presale boolean;
  v_usdt_commission numeric;
  v_share_commission numeric;
  v_existing_balance RECORD;
BEGIN
  -- Lock the conversion row to prevent double processing
  SELECT c.* INTO v_conversion
  FROM commission_conversions c
  WHERE c.id = p_conversion_id
  FOR UPDATE;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Conversion % not found', p_conversion_id;
  END IF;

  IF v_conversion.status <> 'pending' THEN
    RAISE EXCEPTION 'Conversion % is not pending (status=%)', p_conversion_id, v_conversion.status;
  END IF;

  v_user_id := v_conversion.user_id;
  v_required_usdt := v_conversion.usdt_amount;
  v_shares := v_conversion.shares_requested;

  -- Get phase for share price and name
  SELECT p.* INTO v_phase FROM investment_phases p WHERE p.id = v_conversion.phase_id;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Investment phase % not found', v_conversion.phase_id;
  END IF;

  v_is_presale := position('presale' in lower(coalesce(v_phase.phase_name, ''))) > 0
                  OR position('pre-sale' in lower(coalesce(v_phase.phase_name, ''))) > 0;

  -- Deduct USDT from user's commission balance (prefer escrowed)
  UPDATE commission_balances b
  SET
    escrowed_amount = GREATEST(b.escrowed_amount - v_required_usdt, 0),
    usdt_balance = CASE WHEN b.escrowed_amount >= v_required_usdt
                        THEN b.usdt_balance -- already reserved in escrow
                        ELSE b.usdt_balance - (v_required_usdt - b.escrowed_amount)
                   END,
    last_updated = NOW()
  WHERE b.user_id = v_user_id;

  -- Allocate shares to user (portfolio/accounting table)
  INSERT INTO aureus_share_purchases(
    user_id, package_name, shares_purchased, total_amount, commission_used,
    remaining_payment, payment_method, status, created_at, updated_at, phase_id
  ) VALUES (
    v_user_id,
    COALESCE(v_phase.phase_name, 'Commission Conversion'),
    v_shares,
    v_required_usdt,
    v_required_usdt,
    0,
    'commission_conversion',
    'active',
    NOW(), NOW(), v_phase.id
  );

  -- Mark conversion as approved
  UPDATE commission_conversions SET status = 'approved', approved_at = NOW() WHERE id = p_conversion_id;

  -- Generate referral commission (if active referral exists)
  SELECT r.* INTO v_referral
  FROM referrals r
  WHERE r.referred_id = v_user_id AND r.status = 'active'
  ORDER BY r.created_at DESC
  LIMIT 1;

  IF FOUND THEN
    v_usdt_commission := ROUND(v_required_usdt * 0.15, 2);
    v_share_commission := CASE WHEN v_is_presale THEN ROUND(v_shares * 0.15, 2) ELSE 0 END;

    -- Insert commission transaction for referrer
    INSERT INTO commission_transactions (
      referrer_id,
      referred_id,
      share_purchase_id,
      commission_rate,
      share_purchase_amount,
      usdt_commission,
      share_commission,
      commission_source,
      status,
      payment_date,
      created_at
    ) VALUES (
      v_referral.referrer_id,
      v_user_id,
      NULL, -- not a direct share purchase record; set NULL or link to a created record if available
      15.00,
      v_required_usdt,
      v_usdt_commission,
      v_share_commission,
      'commission_conversion',
      'approved',
      NOW(), NOW()
    );

    -- Update referrer's commission balance
    SELECT * INTO v_existing_balance FROM commission_balances WHERE user_id = v_referral.referrer_id;

    IF NOT FOUND THEN
      INSERT INTO commission_balances (
        user_id, usdt_balance, share_balance, total_earned_usdt, total_earned_shares, last_updated, created_at
      ) VALUES (
        v_referral.referrer_id,
        v_usdt_commission,
        v_share_commission,
        v_usdt_commission,
        v_share_commission,
        NOW(), NOW()
      );
    ELSE
      UPDATE commission_balances
      SET
        usdt_balance = COALESCE(usdt_balance,0) + v_usdt_commission,
        share_balance = COALESCE(share_balance,0) + v_share_commission,
        total_earned_usdt = COALESCE(total_earned_usdt,0) + v_usdt_commission,
        total_earned_shares = COALESCE(total_earned_shares,0) + v_share_commission,
        last_updated = NOW()
      WHERE user_id = v_referral.referrer_id;
    END IF;

    -- Optional: increment shares_sold metric for share commissions
    IF v_share_commission > 0 THEN
      PERFORM increment_shares_sold(v_phase.id, v_share_commission, 'referral_commission');
    END IF;
  END IF;

  -- Audit log (optional): could insert into an admin log table
  -- INSERT INTO admin_actions(action_type, action_details, admin_id, admin_username, created_at)
  -- VALUES ('approve_commission_conversion', json_build_object('conversion_id', p_conversion_id), p_admin_id, p_admin_username, NOW());

END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;
