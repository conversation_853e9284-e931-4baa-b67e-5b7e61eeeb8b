const { createClient } = require('@supabase/supabase-js');

// Use the hardcoded credentials from the working files
const supabaseUrl = 'https://fgubaqoftdeefcakejwu.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZndWJhcW9mdGRlZWZjYWtland1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTMwOTIxMCwiZXhwIjoyMDY2ODg1MjEwfQ.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function clearPaymentLock() {
  console.log('🔧 Clearing payment lock for user...');
  
  try {
    // First, let's find all telegram users with payment locks
    const { data: lockedUsers, error: findError } = await supabase
      .from('telegram_users')
      .select('telegram_id, payment_lock_status, payment_context, payment_lock_timestamp')
      .eq('payment_lock_status', true);
    
    if (findError) {
      console.error('❌ Error finding locked users:', findError);
      return;
    }
    
    console.log('🔍 Found locked users:', lockedUsers);
    
    if (lockedUsers && lockedUsers.length > 0) {
      // Clear all payment locks
      const { data: clearResult, error: clearError } = await supabase
        .from('telegram_users')
        .update({
          payment_lock_status: false,
          payment_context: null,
          payment_lock_timestamp: null
        })
        .eq('payment_lock_status', true);
      
      if (clearError) {
        console.error('❌ Error clearing payment locks:', clearError);
        return;
      }
      
      console.log('✅ Payment locks cleared successfully!');
      console.log('📊 Cleared locks for users:', lockedUsers.map(u => u.telegram_id));
    } else {
      console.log('ℹ️ No payment locks found');
    }
    
    // Also check for any incomplete payments that might be causing issues
    const { data: incompletePayments, error: paymentError } = await supabase
      .from('crypto_payment_transactions')
      .select('id, user_id, status, created_at')
      .in('status', ['incomplete', 'pending'])
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (!paymentError && incompletePayments) {
      console.log('💳 Recent incomplete/pending payments:', incompletePayments);
    }
    
  } catch (error) {
    console.error('❌ Error clearing payment lock:', error);
  }
}

clearPaymentLock();
