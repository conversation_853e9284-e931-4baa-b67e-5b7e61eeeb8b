Write-Host "🚀 Pushing Telegram error fixes to git..." -ForegroundColor Green
Write-Host ""

Write-Host "Step 1: Adding modified files..." -ForegroundColor Yellow
git add aureus-bot-new.js

Write-Host "Step 2: Committing changes..." -ForegroundColor Yellow
git commit -m "Fix Telegram parsing errors and message length issues

- Add escapeMarkdown() utility function to safely escape special characters
- Fix portfolio markdown parsing errors with error handling and fallback  
- Fix notification center 'message too long' error with smart truncation
- Add comprehensive error handling for all replyWithMarkdown() calls
- Escape user-generated content in payment reviews and portfolio
- Add graceful fallback to plain text when markdown parsing fails
- Improve error logging for better debugging

Fixes:
- Portfolio error: Can't find end of entity starting at byte offset 525
- Notification center error: message is too long  
- Payment review markdown parsing errors
- All user data now safely escaped for markdown compatibility"

Write-Host "Step 3: Pushing to GitHub (Railway will auto-deploy)..." -ForegroundColor Yellow
git push origin main

Write-Host ""
Write-Host "✅ Git push completed! Railway should now deploy the fixes automatically." -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to continue"
