@echo off
echo Pushing Telegram error fixes to git...
echo.

echo Step 1: Adding modified files...
git add aureus-bot-new.js

echo Step 2: Committing changes...
git commit -m "Fix Telegram parsing errors and message length issues

- Add escapeMarkdown() utility function to safely escape special characters
- Fix portfolio markdown parsing errors with error handling and fallback  
- Fix notification center 'message too long' error with smart truncation
- Add comprehensive error handling for all replyWithMarkdown() calls
- Escape user-generated content in payment reviews and portfolio
- Add graceful fallback to plain text when markdown parsing fails
- Improve error logging for better debugging

Fixes:
- Portfolio error: Can't find end of entity starting at byte offset 525
- Notification center error: message is too long  
- Payment review markdown parsing errors
- All user data now safely escaped for markdown compatibility"

echo Step 3: Pushing to GitHub (Railway will auto-deploy)...
git push origin main

echo.
echo ✅ Git push completed! Railway should now deploy the fixes automatically.
echo.
pause
