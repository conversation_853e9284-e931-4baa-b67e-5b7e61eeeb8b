-- Fix User ID Mapping Conflict Between Web App and Telegram Bot
-- This script safely resolves the conflict where TTTFOUNDER has two user IDs:
-- - Original user ID 4 (with all the referrals and data)
-- - New user ID 116 (created by Telegram bot)

-- ============================================================================
-- STEP 1: SAFETY CHECKS - Verify current state before making changes
-- ============================================================================

-- Check current TTTFOUNDER telegram mapping
SELECT 
  'Current Telegram Mapping' as check_type,
  telegram_id,
  user_id,
  username,
  first_name,
  is_registered
FROM telegram_users 
WHERE telegram_id = 1393852532;

-- Check both user records
SELECT 
  'User Records' as check_type,
  id,
  username,
  full_name,
  email,
  created_at
FROM users 
WHERE id IN (4, 116)
ORDER BY id;

-- Check referrals for both user IDs
SELECT 
  'Referrals Check' as check_type,
  referrer_id,
  COUNT(*) as referral_count
FROM referrals 
WHERE referrer_id IN (4, 116)
GROUP BY referrer_id;

-- Check commission data for both user IDs
SELECT 
  'Commission Balances' as check_type,
  user_id,
  total_earned_usdt,
  total_earned_shares,
  usdt_balance
FROM commission_balances 
WHERE user_id IN (4, 116);

-- Check commission transactions for both user IDs
SELECT 
  'Commission Transactions' as check_type,
  referrer_id,
  COUNT(*) as transaction_count,
  SUM(usdt_commission) as total_usdt,
  SUM(share_commission) as total_shares
FROM commission_transactions 
WHERE referrer_id IN (4, 116)
GROUP BY referrer_id;

-- ============================================================================
-- STEP 2: BACKUP CRITICAL DATA (Optional but recommended)
-- ============================================================================

-- Create backup of telegram_users record before modification
CREATE TABLE IF NOT EXISTS telegram_users_backup_temp AS
SELECT * FROM telegram_users WHERE telegram_id = 1393852532;

-- Create backup of user record that will be deleted
CREATE TABLE IF NOT EXISTS users_backup_temp AS
SELECT * FROM users WHERE id = 116;

-- ============================================================================
-- STEP 3: SAFE DATA MIGRATION
-- ============================================================================

-- Check if user ID 116 has any data that needs to be preserved
-- (This should show if there's any data associated with user ID 116 that we need to migrate)

-- Check for any payments associated with user ID 116
SELECT 'Payments Check' as check_type, COUNT(*) as count
FROM crypto_payment_transactions WHERE user_id = 116;

-- Check for any share purchases associated with user ID 116
SELECT 'Share Purchases Check' as check_type, COUNT(*) as count
FROM aureus_share_purchases WHERE user_id = 116;

-- Check for any other critical data associated with user ID 116
SELECT 'KYC Info Check' as check_type, COUNT(*) as count
FROM kyc_information WHERE user_id = 116;

-- ============================================================================
-- STEP 4: EXECUTE THE FIX (Run these one by one and verify each step)
-- ============================================================================

-- 4A: Update the telegram_users mapping to point to the original user ID 4
UPDATE telegram_users 
SET user_id = 4 
WHERE telegram_id = 1393852532 AND user_id = 116;

-- Verify the update worked
SELECT 
  'Updated Telegram Mapping' as verification,
  telegram_id,
  user_id,
  username,
  first_name
FROM telegram_users 
WHERE telegram_id = 1393852532;

-- 4B: Check if user ID 116 has any remaining references before deletion
SELECT 
  'Remaining References Check' as check_type,
  'telegram_users' as table_name,
  COUNT(*) as count
FROM telegram_users WHERE user_id = 116
UNION ALL
SELECT 
  'Remaining References Check',
  'crypto_payment_transactions',
  COUNT(*)
FROM crypto_payment_transactions WHERE user_id = 116
UNION ALL
SELECT 
  'Remaining References Check',
  'aureus_share_purchases',
  COUNT(*)
FROM aureus_share_purchases WHERE user_id = 116
UNION ALL
SELECT 
  'Remaining References Check',
  'commission_balances',
  COUNT(*)
FROM commission_balances WHERE user_id = 116
UNION ALL
SELECT 
  'Remaining References Check',
  'referrals_as_referrer',
  COUNT(*)
FROM referrals WHERE referrer_id = 116
UNION ALL
SELECT 
  'Remaining References Check',
  'referrals_as_referred',
  COUNT(*)
FROM referrals WHERE referred_id = 116;

-- 4C: Only delete user ID 116 if no references remain (should be safe after step 4A)
-- IMPORTANT: Only run this if the above query shows 0 counts for all tables
DELETE FROM users WHERE id = 116;

-- Verify deletion
SELECT 'User Deletion Verification' as check_type, COUNT(*) as remaining_count
FROM users WHERE id = 116;

-- ============================================================================
-- STEP 5: FINAL VERIFICATION
-- ============================================================================

-- Verify the fix is complete
SELECT 
  'Final State Check' as check_type,
  'telegram_mapping' as detail,
  telegram_id,
  user_id,
  username
FROM telegram_users 
WHERE telegram_id = 1393852532
UNION ALL
SELECT 
  'Final State Check',
  'user_exists',
  NULL,
  id,
  username
FROM users WHERE id = 4;

-- Verify referrals are now accessible
SELECT 
  'Referrals Verification' as check_type,
  referrer_id,
  COUNT(*) as referral_count,
  STRING_AGG(DISTINCT status, ', ') as statuses
FROM referrals 
WHERE referrer_id = 4
GROUP BY referrer_id;

-- ============================================================================
-- STEP 6: CLEANUP (Optional - run after verifying everything works)
-- ============================================================================

-- Remove temporary backup tables (only run after confirming everything works)
-- DROP TABLE IF EXISTS telegram_users_backup_temp;
-- DROP TABLE IF EXISTS users_backup_temp;

-- ============================================================================
-- EXECUTION NOTES:
-- ============================================================================
-- 1. Run each section separately and verify results before proceeding
-- 2. The backup tables will be created automatically for safety
-- 3. If anything goes wrong, you can restore from the backup tables
-- 4. Test the Telegram bot after Step 4 to ensure it works correctly
-- 5. Only run the cleanup (Step 6) after confirming everything is working
-- ============================================================================
