-- Fix auth_tokens table schema
-- Add missing columns for the web authentication flow

-- Add updated_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'auth_tokens' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE auth_tokens ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- Add user_status column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'auth_tokens' AND column_name = 'user_status'
    ) THEN
        ALTER TABLE auth_tokens ADD COLUMN user_status TEXT;
    END IF;
END $$;

-- Add telegram_id column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'auth_tokens' AND column_name = 'telegram_id'
    ) THEN
        ALTER TABLE auth_tokens ADD COLUMN telegram_id BIGINT;
    END IF;
END $$;

-- Add user_data column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'auth_tokens' AND column_name = 'user_data'
    ) THEN
        ALTER TABLE auth_tokens ADD COLUMN user_data TEXT;
    END IF;
END $$;

-- Update existing records to have updated_at = created_at if null
UPDATE auth_tokens 
SET updated_at = COALESCE(updated_at, created_at, NOW())
WHERE updated_at IS NULL;

-- Show the current table structure
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'auth_tokens' 
ORDER BY ordinal_position;
