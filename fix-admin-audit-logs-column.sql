-- Fix admin_audit_logs column name issue
-- The error shows code is trying to use 'admin_id' but the correct column is 'admin_telegram_id'

-- First, let's check if the function exists and drop it if it does
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, BIGINT, VARCHAR);

-- Recreate the function with correct column names
CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_conversion_id UUID,
    p_admin_id BIGINT,
    p_admin_username VARCHAR(255)
)
RETURNS VOID AS $$
DECLARE
    v_conversion_record RECORD;
    v_required_amount DECIMAL(10,2);
    v_escrowed_amount DECIMAL(10,2);
    v_remaining_escrow DECIMAL(10,2);
BEGIN
    -- Get the conversion record
    SELECT * INTO v_conversion_record
    FROM commission_conversions
    WHERE id = p_conversion_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Conversion request not found or not pending';
    END IF;
    
    -- Calculate required amount (shares * $1 per share)
    v_required_amount := v_conversion_record.shares_requested;
    
    -- Get user's escrowed amount
    SELECT COALESCE(SUM(amount), 0) INTO v_escrowed_amount
    FROM commission_escrow
    WHERE user_id = v_conversion_record.user_id AND status = 'escrowed';
    
    -- Check if user has enough escrowed funds
    IF v_escrowed_amount < v_required_amount THEN
        RAISE EXCEPTION 'Insufficient escrowed funds. Required: $%, Available: $%', 
            v_required_amount, v_escrowed_amount;
    END IF;
    
    -- Update conversion status to approved
    UPDATE commission_conversions
    SET 
        status = 'approved',
        approved_by_admin_id = p_admin_id,
        approved_at = NOW(),
        updated_at = NOW()
    WHERE id = p_conversion_id;
    
    -- Clear the required amount from escrow (FIFO - oldest first)
    v_remaining_escrow := v_required_amount;
    
    UPDATE commission_escrow
    SET 
        status = 'cleared',
        cleared_at = NOW(),
        cleared_by_admin_id = p_admin_id
    WHERE user_id = v_conversion_record.user_id 
        AND status = 'escrowed'
        AND amount <= v_remaining_escrow
        AND id IN (
            SELECT id FROM commission_escrow
            WHERE user_id = v_conversion_record.user_id AND status = 'escrowed'
            ORDER BY created_at ASC
            LIMIT (SELECT COUNT(*) FROM commission_escrow 
                   WHERE user_id = v_conversion_record.user_id 
                   AND status = 'escrowed' 
                   AND amount <= v_remaining_escrow)
        );
    
    -- Add shares to user's portfolio
    INSERT INTO share_purchases (
        user_id,
        shares_purchased,
        total_amount,
        payment_method,
        status,
        purchase_type,
        created_at
    ) VALUES (
        v_conversion_record.user_id,
        v_conversion_record.shares_requested,
        v_required_amount,
        'commission_conversion',
        'active',
        'commission_conversion',
        NOW()
    );
    
    -- Log the admin action with correct column names
    BEGIN
        INSERT INTO admin_audit_logs (
            admin_telegram_id,
            admin_username,
            action,
            target_type,
            target_id,
            details,
            timestamp
        ) VALUES (
            p_admin_id,
            p_admin_username,
            'commission_conversion_approval',
            'commission_conversions',
            p_conversion_id::text,
            jsonb_build_object(
                'user_id', v_conversion_record.user_id,
                'usdt_amount', v_required_amount,
                'shares_requested', v_conversion_record.shares_requested,
                'escrow_cleared', v_escrowed_amount
            ),
            NOW()
        );
    EXCEPTION
        WHEN undefined_table THEN
            -- Table doesn't exist, skip logging
            NULL;
        WHEN OTHERS THEN
            -- Log the error but don't fail the transaction
            RAISE NOTICE 'Failed to log admin action: %', SQLERRM;
    END;
    
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, VARCHAR) TO service_role;
