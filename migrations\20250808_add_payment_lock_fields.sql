-- Safe migration script
BEGIN;
ALTER TABLE telegram_users ADD COLUMN IF NOT EXISTS payment_lock_status BOOLEAN DEFAULT FALSE;
ALTER TABLE telegram_users ADD COLUMN IF NOT EXISTS payment_context JSONB;
ALTER TABLE telegram_users ADD COLUMN IF NOT EXISTS payment_lock_timestamp TIMESTAMPTZ;
CREATE INDEX IF NOT EXISTS idx_telegram_users_payment_lock ON telegram_users(payment_lock_status, payment_lock_timestamp);
COMMIT;