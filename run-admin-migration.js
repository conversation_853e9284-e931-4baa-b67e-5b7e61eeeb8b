#!/usr/bin/env node

/**
 * Safe Admin Column Migration Script
 * Adds is_admin column to users table for Task 2 admin verification
 * Production-safe with rollback capability
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Database configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  console.log('🚀 Starting Admin Column Migration for Task 2...\n');

  try {
    // Step 1: Check if column already exists
    console.log('🔍 Step 1: Checking if is_admin column exists...');
    const { data: columnCheck, error: columnError } = await supabase.rpc('exec', {
      sql: `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'is_admin';
      `
    });

    if (columnError) {
      console.error('❌ Error checking column existence:', columnError);
      return false;
    }

    if (columnCheck && columnCheck.length > 0) {
      console.log('✅ is_admin column already exists, skipping creation');
    } else {
      // Step 2: Add is_admin column
      console.log('📋 Step 2: Adding is_admin column to users table...');
      const { error: addColumnError } = await supabase.rpc('exec', {
        sql: `
          ALTER TABLE public.users 
          ADD COLUMN is_admin BOOLEAN DEFAULT FALSE;
        `
      });

      if (addColumnError) {
        console.error('❌ Error adding is_admin column:', addColumnError);
        return false;
      }
      console.log('✅ is_admin column added successfully');
    }

    // Step 3: Set TTTFOUNDER as admin
    console.log('👑 Step 3: Setting TTTFOUNDER as admin...');
    const { data: updateResult, error: updateError } = await supabase.rpc('exec', {
      sql: `
        UPDATE public.users 
        SET is_admin = TRUE 
        WHERE username = 'TTTFOUNDER'
        RETURNING username, is_admin;
      `
    });

    if (updateError) {
      console.error('❌ Error setting TTTFOUNDER as admin:', updateError);
      return false;
    }

    if (updateResult && updateResult.length > 0) {
      console.log('✅ TTTFOUNDER set as admin successfully');
    } else {
      console.log('⚠️ TTTFOUNDER user not found - will need to be set manually');
    }

    // Step 4: Create performance index
    console.log('⚡ Step 4: Creating performance index...');
    const { error: indexError } = await supabase.rpc('exec', {
      sql: `
        CREATE INDEX IF NOT EXISTS idx_users_is_admin 
        ON public.users(is_admin) 
        WHERE is_admin = TRUE;
      `
    });

    if (indexError) {
      console.log('⚠️ Warning: Could not create index (non-critical):', indexError.message);
    } else {
      console.log('✅ Performance index created successfully');
    }

    // Step 5: Verification
    console.log('🔍 Step 5: Verifying migration...');
    const { data: verifyData, error: verifyError } = await supabase.rpc('exec', {
      sql: `
        SELECT username, is_admin, created_at 
        FROM public.users 
        WHERE is_admin = TRUE;
      `
    });

    if (verifyError) {
      console.error('❌ Error verifying migration:', verifyError);
      return false;
    }

    console.log('\n🎉 Migration completed successfully!');
    console.log('\n📊 Admin Users:');
    if (verifyData && verifyData.length > 0) {
      verifyData.forEach(user => {
        console.log(`   👑 ${user.username} (Admin: ${user.is_admin})`);
      });
    } else {
      console.log('   ⚠️ No admin users found');
    }

    console.log('\n✅ Task 2 admin verification is now ready to use!');
    return true;

  } catch (error) {
    console.error('💥 Unexpected error during migration:', error);
    return false;
  }
}

async function main() {
  console.log('🔐 Admin Column Migration Script');
  console.log('================================\n');

  const success = await runMigration();

  if (success) {
    console.log('\n🎯 Next Steps:');
    console.log('1. Test admin panel access with TTTFOUNDER account');
    console.log('2. Verify non-admin users are blocked');
    console.log('3. Check logs for admin verification messages');
    console.log('4. Proceed with Task 2 testing protocol');
    process.exit(0);
  } else {
    console.log('\n❌ Migration failed. Please check errors above.');
    console.log('\n🔄 Rollback command (if needed):');
    console.log('   ALTER TABLE public.users DROP COLUMN IF EXISTS is_admin;');
    process.exit(1);
  }
}

// Run the migration
main().catch(console.error);
