-- Add user_preferences table for storing user language preferences and other settings
-- This table will store user preferences like presentation language selection

CREATE TABLE IF NOT EXISTS user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  preference_type VARCHAR(100) NOT NULL,
  preference_value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure one preference per type per user
  CONSTRAINT unique_user_preference UNIQUE(user_id, preference_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_type ON user_preferences(preference_type);

-- Add some sample preference types for documentation
COMMENT ON TABLE user_preferences IS 'Stores user preferences like language selection, notification settings, etc.';
COMMENT ON COLUMN user_preferences.preference_type IS 'Type of preference: presentation_language, notification_settings, etc.';
COMMENT ON COLUMN user_preferences.preference_value IS 'Value of the preference: english, hindi, french, bengali, etc.';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON user_preferences TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON user_preferences TO service_role;
