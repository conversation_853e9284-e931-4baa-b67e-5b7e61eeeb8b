# 🚨 URGENT: Database Function Fix Required

## ⚠️ Current Error
```
Commission conversion transaction error: {
  code: '42703',
  details: null,
  hint: null,
  message: 'column "admin_id" of relation "admin_audit_logs" does not exist'
}
```

## 🔍 Root Cause
The `process_commission_conversion` database function is using incorrect column names:
- ❌ Using: `admin_id` 
- ✅ Should be: `admin_telegram_id`
- ❌ Using: `action_type`
- ✅ Should be: `action`

## 🔧 IMMEDIATE FIX REQUIRED

**Run this SQL in your Supabase SQL Editor:**

```sql
-- Fix the process_commission_conversion function
DROP FUNCTION IF EXISTS process_commission_conversion(UUID, BIGINT, VARCHAR);

CREATE OR REPLACE FUNCTION process_commission_conversion(
    p_conversion_id UUID,
    p_admin_id BIGINT,
    p_admin_username VARCHAR(255)
)
RETURNS VOID AS $$
DECLARE
    v_conversion_record RECORD;
    v_required_amount DECIMAL(10,2);
    v_escrowed_amount DECIMAL(10,2);
BEGIN
    -- Get the conversion record
    SELECT * INTO v_conversion_record
    FROM commission_conversions
    WHERE id = p_conversion_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Conversion request not found or not pending';
    END IF;
    
    -- Calculate required amount
    v_required_amount := v_conversion_record.shares_requested;
    
    -- Get user's escrowed amount
    SELECT COALESCE(SUM(amount), 0) INTO v_escrowed_amount
    FROM commission_escrow
    WHERE user_id = v_conversion_record.user_id AND status = 'escrowed';
    
    -- Check if user has enough escrowed funds
    IF v_escrowed_amount < v_required_amount THEN
        RAISE EXCEPTION 'Insufficient escrowed funds. Required: $%, Available: $%', 
            v_required_amount, v_escrowed_amount;
    END IF;
    
    -- Update conversion status
    UPDATE commission_conversions
    SET 
        status = 'approved',
        approved_by_admin_id = p_admin_id,
        approved_at = NOW(),
        updated_at = NOW()
    WHERE id = p_conversion_id;
    
    -- Clear escrow funds
    UPDATE commission_escrow
    SET 
        status = 'cleared',
        cleared_at = NOW(),
        cleared_by_admin_id = p_admin_id
    WHERE user_id = v_conversion_record.user_id 
        AND status = 'escrowed'
        AND amount <= v_required_amount;
    
    -- Add shares to portfolio
    INSERT INTO share_purchases (
        user_id,
        shares_purchased,
        total_amount,
        payment_method,
        status,
        purchase_type,
        created_at
    ) VALUES (
        v_conversion_record.user_id,
        v_conversion_record.shares_requested,
        v_required_amount,
        'commission_conversion',
        'active',
        'commission_conversion',
        NOW()
    );
    
    -- Log admin action with CORRECT column names
    BEGIN
        INSERT INTO admin_audit_logs (
            admin_telegram_id,
            admin_username,
            action,
            target_type,
            target_id,
            details,
            timestamp
        ) VALUES (
            p_admin_id,
            p_admin_username,
            'commission_conversion_approval',
            'commission_conversions',
            p_conversion_id::text,
            jsonb_build_object(
                'user_id', v_conversion_record.user_id,
                'usdt_amount', v_required_amount,
                'shares_requested', v_conversion_record.shares_requested,
                'escrow_cleared', v_escrowed_amount
            ),
            NOW()
        );
    EXCEPTION
        WHEN OTHERS THEN
            -- Don't fail the transaction if logging fails
            RAISE NOTICE 'Failed to log admin action: %', SQLERRM;
    END;
    
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, VARCHAR) TO authenticated;
GRANT EXECUTE ON FUNCTION process_commission_conversion(UUID, BIGINT, VARCHAR) TO service_role;
```

## 🎯 What This Fixes

1. **✅ Column Name Corrections:**
   - `admin_id` → `admin_telegram_id`
   - `action_type` → `action`
   - `created_at` → `timestamp`

2. **✅ Error Handling:**
   - Won't fail the entire transaction if logging fails
   - Proper exception handling

3. **✅ Commission Conversion Process:**
   - Validates escrowed funds
   - Updates conversion status
   - Clears escrow
   - Adds shares to portfolio
   - Logs admin action

## 🚀 Expected Result

After running this SQL:
- ✅ Commission conversions will work without errors
- ✅ Escrow processing will complete successfully  
- ✅ Admin audit logging will work properly
- ✅ No more "column admin_id does not exist" errors

## ⚡ URGENT ACTION REQUIRED

1. **Open Supabase Dashboard**
2. **Go to SQL Editor**
3. **Paste and run the SQL above**
4. **Test commission conversion**

This will immediately fix the Railway deployment error!
