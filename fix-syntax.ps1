# Fix syntax error by removing orphaned brackets around line 6077
$content = Get-Content "aureus-bot-new.js"
$newContent = @()

for($i = 0; $i -lt $content.Length; $i++) {
    $lineNum = $i + 1
    $line = $content[$i]

    # Skip orphaned brackets/braces and catch blocks in the problematic area (lines 6076-6085)
    if ($lineNum -ge 6076 -and $lineNum -le 6085) {
        if ($line -match '^\s*\]$' -or $line -match '^\s*\}$' -or $line -match '^\s*\}\)\;$' -or
            $line -match '^\s*\}\s*catch\s*\(' -or $line -match '^\s*console\.error' -or
            $line -match '^\s*await ctx\.reply.*Error processing withdrawal') {
            Write-Host "Removing orphaned syntax on line $lineNum`: $line"
            continue
        }
    }

    $newContent += $line
}

$newContent | Set-Content "aureus-bot-new.js"
Write-Host "Fixed syntax errors by removing orphaned brackets/braces"
