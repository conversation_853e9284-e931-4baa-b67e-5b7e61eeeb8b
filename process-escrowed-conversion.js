const { db } = require('./src/database/supabase-client');

async function processEscrowedCommissionConversion() {
  console.log('🔧 Processing Escrowed Commission Conversion...\n');

  try {
    // Step 1: Check user 88's current situation
    console.log('📊 Checking user 88 commission status...');
    const { data: balance, error: balanceError } = await db.client
      .from('commission_balances')
      .select('*')
      .eq('user_id', 88)
      .single();

    if (balanceError) {
      console.log('❌ Could not find user balance:', balanceError.message);
      return;
    }

    console.log('💰 Current balance:', JSON.stringify(balance, null, 2));

    const usdtBalance = parseFloat(balance.usdt_balance || 0);
    const escrowedAmount = parseFloat(balance.escrowed_amount || 0);
    const shareBalance = parseFloat(balance.share_balance || 0);
    const availableBalance = usdtBalance - escrowedAmount;

    console.log(`\n📈 Balance Analysis:`);
    console.log(`USDT Balance: $${usdtBalance}`);
    console.log(`Escrowed: $${escrowedAmount}`);
    console.log(`Available: $${availableBalance}`);
    console.log(`Share Balance: ${shareBalance} shares`);

    // Step 2: Look for pending commission conversions
    console.log('\n🔍 Checking for pending commission conversions...');
    const { data: conversions, error: conversionError } = await db.client
      .from('commission_conversions')
      .select('*')
      .eq('user_id', 88)
      .eq('status', 'pending');

    if (conversionError) {
      console.log('⚠️ Could not check conversions:', conversionError.message);
    } else {
      console.log(`📋 Found ${conversions.length} pending conversions`);
      
      if (conversions.length > 0) {
        conversions.forEach(conversion => {
          console.log(`\n🔄 Conversion Details:`);
          console.log(`ID: ${conversion.id}`);
          console.log(`USDT Amount: $${conversion.usdt_amount}`);
          console.log(`Shares Requested: ${conversion.shares_requested}`);
          console.log(`Share Price: $${conversion.current_share_price}`);
          console.log(`Created: ${conversion.created_at}`);
          
          // Check if this conversion can be processed from escrow
          const conversionAmount = parseFloat(conversion.usdt_amount);
          if (escrowedAmount >= conversionAmount) {
            console.log(`✅ ESCROW STATUS: Sufficient escrowed funds ($${escrowedAmount} >= $${conversionAmount})`);
            console.log(`🎯 ACTION: Admin can process this conversion from escrowed funds`);
          } else if (availableBalance >= conversionAmount) {
            console.log(`✅ AVAILABLE STATUS: Sufficient available funds ($${availableBalance} >= $${conversionAmount})`);
            console.log(`🎯 ACTION: Admin can process this conversion from available funds`);
          } else {
            console.log(`❌ INSUFFICIENT: Not enough funds for conversion`);
          }
        });

        // Step 3: Simulate the admin processing (if we had admin credentials)
        console.log('\n🔧 ADMIN PROCESSING SIMULATION:');
        console.log('To process this conversion manually, an admin would:');
        console.log('1. Verify the user has sufficient escrowed funds ✅');
        console.log('2. Deduct USDT from balance and escrow');
        console.log('3. Add shares to the user\'s share balance');
        console.log('4. Mark conversion as "approved"');
        
        // Calculate what the balances would be after processing
        const firstConversion = conversions[0];
        const conversionAmount = parseFloat(firstConversion.usdt_amount);
        const sharesRequested = parseFloat(firstConversion.shares_requested);
        
        console.log('\n📊 AFTER PROCESSING CALCULATION:');
        console.log(`New USDT Balance: $${usdtBalance - conversionAmount}`);
        console.log(`New Escrowed Amount: $${escrowedAmount - conversionAmount}`);
        console.log(`New Share Balance: ${shareBalance + sharesRequested} shares`);
        console.log(`New Available Balance: $${(usdtBalance - conversionAmount) - (escrowedAmount - conversionAmount)}`);

        console.log('\n🎯 MANUAL FIX REQUIRED:');
        console.log('Execute this SQL in Supabase to process the conversion:');
        console.log('```sql');
        console.log(`-- Process commission conversion for user 88`);
        console.log(`UPDATE commission_balances SET`);
        console.log(`  usdt_balance = ${usdtBalance - conversionAmount},`);
        console.log(`  escrowed_amount = ${escrowedAmount - conversionAmount},`);
        console.log(`  share_balance = ${shareBalance + sharesRequested},`);
        console.log(`  last_updated = NOW()`);
        console.log(`WHERE user_id = 88;`);
        console.log('');
        console.log(`UPDATE commission_conversions SET`);
        console.log(`  status = 'approved',`);
        console.log(`  processed_at = NOW()`);
        console.log(`WHERE id = '${firstConversion.id}';`);
        console.log('```');

      } else {
        console.log('ℹ️ No pending conversions found for user 88');
      }
    }

    // Step 4: Check if the constraint issue is resolved
    console.log('\n🔧 Testing constraint fix...');
    const { data: testUpdate, error: updateError } = await db.client
      .from('commission_balances')
      .update({ last_updated: new Date().toISOString() })
      .eq('user_id', 88);

    if (updateError) {
      console.log('❌ Constraint still blocking updates:', JSON.stringify(updateError, null, 2));
      console.log('\n🎯 SQL FIX STILL NEEDED:');
      console.log('Run the fix-admin-conversion-process.sql file in Supabase SQL Editor');
    } else {
      console.log('✅ Commission balance updates now working!');
      console.log('✅ Admin can process commission conversions from escrow');
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the analysis
processEscrowedCommissionConversion().then(() => {
  console.log('\n✅ Escrowed commission conversion analysis completed');
  console.log('\n📋 SUMMARY:');
  console.log('• The escrow system is working correctly');
  console.log('• User 88 has funds escrowed for commission conversion');
  console.log('• Admin needs to process the conversion from escrowed funds');
  console.log('• Database constraint needs to be removed to allow processing');
  process.exit(0);
}).catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
