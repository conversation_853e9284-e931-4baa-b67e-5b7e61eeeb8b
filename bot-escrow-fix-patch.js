// Fix Bot Admin Commission Conversion Logic
// This patch updates the bot to properly handle escrowed commission conversions

// CURRENT PROBLEM:
// The bot checks: if (availableUSDT < conversion.usdt_amount) 
// This fails for escrowed conversions because availableUSDT = 0 when funds are escrowed

// SOLUTION:
// Check total funds (available + escrowed) instead of just available funds

// File: aureus-bot-new.js
// Function: handleApproveCommissionConversion (around line 3940-3960)

// REPLACE THIS CODE:
/*
    // Verify user still has sufficient commission balance
    const { data: commissionBalance, error: balanceError } = await db.client
      .from('commission_balances')
      .select('usdt_balance')
      .eq('user_id', conversion.user_id)
      .single();

    const availableUSDT = commissionBalance ? parseFloat(commissionBalance.usdt_balance || 0) : 0;

    if (availableUSDT < conversion.usdt_amount) {
      await ctx.replyWithMarkdown(`❌ **INSUFFICIENT USER COMMISSION BALANCE**

**User:** ${conversion.users.full_name || conversion.users.username}
**Required:** $${conversion.usdt_amount.toFixed(2)} USDT
**Available:** $${availableUSDT.toFixed(2)} USDT

Cannot approve this conversion due to insufficient balance.`);
      return;
    }
*/

// WITH THIS CODE:
/*
    // Verify user has sufficient commission balance (available OR escrowed)
    const { data: commissionBalance, error: balanceError } = await db.client
      .from('commission_balances')
      .select('usdt_balance, escrowed_amount')
      .eq('user_id', conversion.user_id)
      .single();

    if (balanceError || !commissionBalance) {
      await ctx.replyWithMarkdown(`❌ **COMMISSION BALANCE ERROR**

**User:** ${conversion.users.full_name || conversion.users.username}
**Error:** Unable to retrieve commission balance

Please check the user's commission account.`);
      return;
    }

    const totalUSDT = parseFloat(commissionBalance.usdt_balance || 0);
    const escrowedUSDT = parseFloat(commissionBalance.escrowed_amount || 0);
    const availableUSDT = totalUSDT - escrowedUSDT;
    const requiredUSDT = parseFloat(conversion.usdt_amount);

    // Check if sufficient funds exist (either available or escrowed)
    if (escrowedUSDT >= requiredUSDT) {
      // Funds are escrowed - this is expected for commission conversions
      console.log(`✅ [ESCROW] Processing conversion from escrowed funds: $${escrowedUSDT} >= $${requiredUSDT}`);
    } else if (availableUSDT >= requiredUSDT) {
      // Funds are available - also valid
      console.log(`✅ [AVAILABLE] Processing conversion from available funds: $${availableUSDT} >= $${requiredUSDT}`);
    } else {
      // Insufficient total funds
      await ctx.replyWithMarkdown(`❌ **INSUFFICIENT USER COMMISSION BALANCE**

**User:** ${conversion.users.full_name || conversion.users.username}
**Required:** $${requiredUSDT.toFixed(2)} USDT
**Available:** $${availableUSDT.toFixed(2)} USDT
**Escrowed:** $${escrowedUSDT.toFixed(2)} USDT
**Total:** $${totalUSDT.toFixed(2)} USDT

⚠️ **Cannot approve:** User needs at least $${requiredUSDT.toFixed(2)} USDT in available or escrowed funds.

${escrowedUSDT > 0 ? '💡 **Note:** Escrowed funds suggest a pending conversion request.' : ''}`);
      return;
    }
*/

// EXPLANATION:
// 1. Now checks BOTH available AND escrowed funds
// 2. Allows processing when funds are properly escrowed (expected behavior)
// 3. Provides better error messages showing escrow status
// 4. The process_commission_conversion function handles the actual escrow logic safely

// BENEFITS:
// ✅ Fixes User 88's immediate problem (and all future escrowed conversions)
// ✅ Maintains proper balance validation
// ✅ Better admin visibility into escrow status
// ✅ Preserves double-spending protection

console.log('📋 Bot patch required for proper escrow handling in admin commission conversions');
console.log('🔧 Apply the code replacement above to aureus-bot-new.js handleApproveCommissionConversion function');
